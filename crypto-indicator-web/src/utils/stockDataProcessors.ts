import { CURRENCIES } from '@/constants/app';
import type { StockIndicatorValueDto, StockStatisticsDto } from '@/generated';
import { filterByCurrency } from '@/utils/dataProcessors';

/**
 * Process stock statistics data for table display
 * Separates USD and BTC data properly to prevent data corruption
 */
export const processStockStatistics = (statistics: StockStatisticsDto[]) => {
  const allStatistics = statistics.filter(
    (stat): stat is StockStatisticsDto => stat.indicatorValues.length > 0,
  );

  // Separate USD and BTC data properly (like crypto processing)
  const usdStatistics = filterByCurrency(allStatistics, CURRENCIES.USD).sort(
    (a, b) => a.symbol.localeCompare(b.symbol),
  );
  const btcStatistics = filterByCurrency(allStatistics, CURRENCIES.BTC);

  return {
    stockStatistics: usdStatistics as StockStatisticsDto[], // Only USD data for main table
    btcStatistics: btcStatistics as StockStatisticsDto[],
    totalCount: usdStatistics.length,
  };
};

/**
 * Get the latest indicator value for a stock
 */
export const getLatestStockData = (
  stock: StockStatisticsDto,
): StockIndicatorValueDto | undefined => {
  if (stock.indicatorValues.length === 0) {
    return undefined;
  }

  // Return the most recent indicator value (last in array)
  return stock.indicatorValues.at(-1);
};
