import { findBtcDataForSymbol } from "@/utils/dataProcessors";
import { matchesSignalFilter } from "@/utils/tableFiltering";

import { getLatestStockData } from "./stockDataProcessors";

import type { StockStatisticsDto } from "@/generated";
import type { StockFilterConfig } from "@/types/table";

/**
 * Apply filters to stock data
 */
export const applyStockFilters = (
  data: StockStatisticsDto[],
  btcStatistics: StockStatisticsDto[],
  filterConfig: StockFilterConfig,
): StockStatisticsDto[] => {
  return data.filter((stock) => {
    const latestData = getLatestStockData(stock);
    const btcData = findBtcDataForSymbol(btcStatistics, stock.symbol);

    // Symbol search filter
    if (filterConfig.symbolSearch) {
      const searchTerm = filterConfig.symbolSearch.toLowerCase();
      const symbolMatch = stock.symbol.toLowerCase().includes(searchTerm);
      const nameMatch = stock.mapping?.name?.toLowerCase().includes(searchTerm) ?? false;

      if (!symbolMatch && !nameMatch) {
        return false;
      }
    }

    // USD signal filter
    if (!matchesSignalFilter(latestData?.color, filterConfig.usdSignal)) {
      return false;
    }

    // BTC signal filter
    return matchesSignalFilter(btcData?.color, filterConfig.btcSignal);
  });
};
